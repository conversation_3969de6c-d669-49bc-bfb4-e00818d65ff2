/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('primevue/avatar')['default']
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Button: typeof import('primevue/button')['default']
    Chart: typeof import('primevue/chart')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Column: typeof import('primevue/column')['default']
    DataTable: typeof import('primevue/datatable')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    FloatingConfigurator: typeof import('./src/components/FloatingConfigurator.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Menu: typeof import('primevue/menu')['default']
    Message: typeof import('primevue/message')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    Password: typeof import('primevue/password')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectButton: typeof import('primevue/selectbutton')['default']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    Toast: typeof import('primevue/toast')['default']
    ToggleSwitch: typeof import('primevue/toggleswitch')['default']
    Toolbar: typeof import('primevue/toolbar')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
  }
}
